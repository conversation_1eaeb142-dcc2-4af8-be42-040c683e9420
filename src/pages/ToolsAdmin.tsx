import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Plus, ArrowLeft, Trash2 } from 'lucide-react';
import { useTools } from '../hooks/useTools';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { toast } from '../components/ui/use-toast';
import { icons } from 'lucide-react';
import IconRenderer from '../components/IconRenderer';

const ToolsAdmin = () => {
  const navigate = useNavigate();
  const { categories, addTool, addCategory, deleteTool, deleteCategory } = useTools();
  const [newTool, setNewTool] = useState({
    name: '',
    description: '',
    url: '',
    category_id: '',
    tags: [] as string[],
    featured: false
  });
  const [newCategory, setNewCategory] = useState({
    title: '',
    icon: '',
    description: '',
    order_field: 0  // Changed from order to order_field
  });
  const [iconPreview, setIconPreview] = useState<string>('');
  const availableIcons = Object.keys(icons);

  const handleAddTool = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addTool.mutateAsync(newTool);
      setNewTool({
        name: '',
        description: '',
        url: '',
        category_id: '',
        tags: [],
        featured: false
      });
      toast({
        title: 'Success',
        description: 'Tool added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add tool',
        variant: 'destructive',
      });
    }
  };

  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addCategory.mutateAsync(newCategory);
      setNewCategory({
        title: '',
        icon: '',
        description: '',
        order_field: 0
      });
      toast({
        title: 'Success',
        description: 'Category added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add category',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-midnight to-midnight/95 text-snow p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <motion.button
          onClick={() => navigate('/tools')}
          className="inline-flex items-center px-4 py-2 rounded-xl bg-white/[0.03] hover:bg-white/[0.06]
            border border-white/10 hover:border-accent/20 transition-all duration-300 text-snow/70
            hover:text-accent group backdrop-blur-sm"
          whileHover={{ scale: 1.02, x: -2 }}
          whileTap={{ scale: 0.98 }}
        >
          <motion.div
            className="mr-2"
            whileHover={{ x: -2 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowLeft className="w-4 h-4" />
          </motion.div>
          <span className="font-medium">Back to Tools</span>
        </motion.button>
      </motion.div>

      <motion.div
        className="max-w-6xl mx-auto grid md:grid-cols-2 gap-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {/* Add Category Form */}
        <motion.div
          className="bg-white/[0.02] rounded-2xl p-6 border border-white/10 backdrop-blur-sm
                   hover:bg-white/[0.04] hover:border-accent/20 transition-all duration-300"
          whileHover={{ y: -2, scale: 1.01 }}
        >
          <div className="flex items-center gap-3 mb-6">
            <motion.div
              className="p-2 bg-accent/10 rounded-xl border border-accent/20"
              whileHover={{ scale: 1.1, rotate: 5 }}
            >
              <Plus className="w-5 h-5 text-accent" />
            </motion.div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-accent to-accent/80 bg-clip-text text-transparent">
              Add Category
            </h2>
          </div>
          <form onSubmit={handleAddCategory} className="space-y-4">
            <div>
              <label className="block text-sm mb-2">Title</label>
              <Input
                value={newCategory.title}
                onChange={(e) => setNewCategory({ ...newCategory, title: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Icon</label>
              <div className="space-y-2">
                <Input
                  value={newCategory.icon}
                  onChange={(e) => {
                    const value = e.target.value;
                    setNewCategory({ ...newCategory, icon: value });
                    setIconPreview(value);
                  }}
                  list="icon-suggestions"
                  placeholder="Enter Lucide icon name"
                  required
                />
                <datalist id="icon-suggestions">
                  {availableIcons.map(icon => (
                    <option key={icon} value={icon} />
                  ))}
                </datalist>
                {iconPreview && (
                  <div className="flex items-center gap-2 p-2 bg-white/5 rounded">
                    <IconRenderer name={iconPreview} className="w-5 h-5 text-emerald-500" />
                    {!icons[iconPreview as keyof typeof icons] && (
                      <span className="text-xs text-red-400">Icon not found</span>
                    )}
                  </div>
                )}
                <p className="text-xs text-snow/60">
                  Use Lucide icon names from{' '}
                  <a 
                    href="https://lucide.dev/icons" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-emerald-500 hover:underline"
                  >
                    lucide.dev/icons
                  </a>
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm mb-2">Description</label>
              <Textarea
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Order</label>
              <Input
                type="number"
                value={newCategory.order_field}
                onChange={(e) => setNewCategory({ 
                  ...newCategory, 
                  order_field: parseInt(e.target.value) 
                })}
                required
              />
            </div>
            <motion.button
              type="submit"
              className="w-full bg-accent hover:bg-accent/80 text-white px-6 py-3 rounded-xl
                       transition-all duration-300 font-medium flex items-center justify-center gap-2
                       hover:shadow-lg hover:shadow-accent/20"
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.div
                whileHover={{ rotate: 90 }}
                transition={{ duration: 0.2 }}
              >
                <Plus className="w-4 h-4" />
              </motion.div>
              Add Category
            </motion.button>
          </form>
        </motion.div>

        {/* Add Tool Form */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h2 className="text-2xl font-bold mb-6">Add Tool</h2>
          <form onSubmit={handleAddTool} className="space-y-4">
            <div>
              <label className="block text-sm mb-2">Name</label>
              <Input
                value={newTool.name}
                onChange={(e) => setNewTool({ ...newTool, name: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Description</label>
              <Textarea
                value={newTool.description}
                onChange={(e) => setNewTool({ ...newTool, description: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">URL</label>
              <Input
                type="url"
                value={newTool.url}
                onChange={(e) => setNewTool({ ...newTool, url: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Category</label>
              <select
                value={newTool.category_id}
                onChange={(e) => setNewTool({ ...newTool, category_id: e.target.value })}
                className="w-full bg-white/5 border border-white/10 rounded-lg p-2"
                required
              >
                <option value="">Select category</option>
                {categories?.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm mb-2">Tags (comma-separated)</label>
              <Input
                value={newTool.tags.join(', ')}
                onChange={(e) => setNewTool({ ...newTool, tags: e.target.value.split(',').map(t => t.trim()) })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newTool.featured}
                onChange={(e) => setNewTool({ ...newTool, featured: e.target.checked })}
                className="rounded border-white/20 bg-white/5"
              />
              <label className="text-sm">Featured</label>
            </div>
            <Button type="submit" className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              Add Tool
            </Button>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default ToolsAdmin;
