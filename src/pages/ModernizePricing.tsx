import React from 'react';
import PricingCard from '../components/PricingCard';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const pricingPlans = [
  {
    title: 'Starter',
    price: 'R250',
    annualPrice: 'R2400',
    savePercentage: 20,
    description: 'For individuals & small businesses looking for a simple online presence.',
    features: [
      'AI-powered chatbot (basic)',
      'Custom domain name',
      'Secure hosting',
      'Mobile-responsive design',
      '1x - Free Monthly maintenance & updates'
    ],
    featureDetails: {
      'AI-powered chatbot (basic)': 'Handles common customer queries with predetermined responses',
      'Custom domain name': 'Get a professional web address and email for your business',
    },
    recommendation: 'Perfect for personal sites & small business pages.'
  },
  {
    title: 'Pro',
    price: 'R750',
    annualPrice: 'R7200',
    savePercentage: 20,
    description: 'For growing businesses that need more engagement & automation.',
    features: [
      'Everything in Starter, plus:',
      'AI chatbot with knowledge base integration',
      'Contact forms & lead capture',
      'Admin content management system',
      'Enhanced security features',
      '3x - Free Monthly maintenance & updates'
    ],
    featureDetails: {
      'AI chatbot with knowledge base integration (RAG)': 'Integrates with your knowledge base to provide accurate responses',
      'Contact forms & lead capture': 'Capture leads and inquiries directly from your website',
    },
    recommendation: 'Ideal for startups & service providers.',
    isPopular: true
  },
  {
    title: 'Business',
    price: 'R1500',
    annualPrice: 'R14400',
    savePercentage: 20,
    description: 'For businesses requiring custom functionality & scalability.',
    features: [
      'Everything in Pro, plus:',
      'AI chatbot advanced support',
      'Advanced SEO optimization',
      'User management & authentication',
      'Payment gateway integration',
      'Basic Analytics & reporting',
      '5x - Free Monthly maintenance & updates'
    ],
    featureDetails: {
      'AI chatbot advanced support': 'Advanced AI chatbot with machine learning capabilities',
      'Advanced SEO optimization': 'Improve your website’s visibility on search engines',
      'User management & authentication': 'Manage user accounts and access permissions',
      'Payment gateway integration': 'Accept online payments directly on your website',

    },
    recommendation: 'Great for growing businesses, online stores, booking sites.'
  },
  {
    title: 'Enterprise',
    price: 'Custom',
    description: 'For enterprises needing fully tailored solutions.',
    features: [
      'Everything in Business, plus:',
      'Fully custom AI chatbot with Automation capabilities',
      'Dedicated server & cloud infrastructure',
      'Priority support & monitoring',
      'Enterprise-grade security & compliance'
    ],
    featureDetails: {
      'Fully custom AI chatbot with deep learning capabilities': 'Tailored AI chatbot with advanced learning capabilities',
      'Dedicated server & cloud infrastructure': 'Exclusive server and cloud resources for your business',
      'Custom integrations & automation':'Integrate with your existing systems and automate workflows'
    },
    recommendation: 'Designed for high-traffic & large-scale operations.'
  }
];

const faqs = [
  {
    question: "How does the AI chatbot work?",
    answer: "Our AI chatbot uses advanced natural language processing to understand and respond to your visitors' queries in real-time, providing accurate and contextual responses based on your website's content."
  },
  {
    question: "Can I upgrade or downgrade my plan?",
    answer: "Yes, you can change your plan at any time. The changes will be reflected in your next billing cycle."
  },
  {
    question: "What kind of support do you offer?",
    answer: "We provide email support for all plans, with priority support and 24/7 monitoring for Enterprise customers."
  }
];

const ModernizePricing = () => {
  return (
    <div className="min-h-screen bg-midnight text-snow">
      <Link to="/">
        <motion.div
          className="fixed top-8 left-8 z-50 flex items-center gap-2 px-4 py-2 rounded-xl
                     bg-white/5 hover:bg-white/10 border border-white/20 backdrop-blur-md
                     text-snow/80 hover:text-accent transition-all duration-300 group"
          whileHover={{ scale: 1.05, x: -2 }}
          whileTap={{ scale: 0.95 }}
        >
          <motion.div
            whileHover={{ x: -2 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowLeft className="w-4 h-4" />
          </motion.div>
          <span className="font-medium">Back to Home</span>
        </motion.div>
      </Link>

      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.h1
              className="text-5xl md:text-6xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <span className="bg-gradient-to-r from-accent to-accent/80 text-transparent bg-clip-text">
                Pricing & Plans
              </span>
            </motion.h1>
            <p className="text-xl text-snow/80 max-w-3xl mx-auto">
              Build intelligent websites and integrate AI-driven customer support for an enhanced user experience.
            </p>
          </div>
          
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block bg-accent/10 border border-accent/20 rounded-xl px-6 py-3 backdrop-blur-sm"
              whileHover={{ scale: 1.02, y: -1 }}
            >
              <span className="text-accent font-semibold">
                30-Day Money-Back Guarantee • No Credit Card Required
              </span>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <PricingCard {...plan} />
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12 mb-32">
            <p className="text-snow/60 text-sm">
              Not sure which plan is right for you?{' '}
              <a href="#" className="text-accent hover:text-accent/80 underline transition-colors">
                Compare features in detail
              </a>
              {' '}or{' '}
              <a href="#" className="text-accent hover:text-accent/80 underline transition-colors">
                schedule a demo
              </a>
            </p>
            <p className="text-snow/60 text-sm mt-4">
              Interested in automation services?{' '}
              <Link to="/services/automation" className="text-accent hover:text-accent/80 underline transition-colors">
                Learn more about our automation services
              </Link>
            </p>
          </div>

          <div className="mt-32">
            <motion.h2
              className="text-3xl font-bold text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <span className="bg-gradient-to-r from-accent to-accent/80 text-transparent bg-clip-text">
                Frequently Asked Questions
              </span>
            </motion.h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  className="bg-white/5 backdrop-blur-md border border-white/20 rounded-xl p-6
                           hover:bg-white/10 hover:border-accent/20 transition-all duration-300 group"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -2, scale: 1.01 }}
                >
                  <h3 className="text-xl font-semibold mb-3 text-accent group-hover:text-accent/80 transition-colors">
                    {faq.question}
                  </h3>
                  <p className="text-snow/80 leading-relaxed">{faq.answer}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernizePricing;
