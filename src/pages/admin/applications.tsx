import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { getJoinApplications } from '@/lib/supabase'
import { JoinApplication } from '@/types/join'
import { ChevronDown, ChevronUp, Mail, Phone, Globe, User, Briefcase, Clock, Target } from 'lucide-react'

export default function ApplicationsAdmin() {
  const [applications, setApplications] = useState<JoinApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [expandedId, setExpandedId] = useState<string | null>(null)

  useEffect(() => {
    const loadApplications = async () => {
      try {
        const data = await getJoinApplications()
        setApplications(data)
      } catch (error) {
        console.error('Error loading applications:', error)
      } finally {
        setLoading(false)
      }
    }

    loadApplications()
  }, [])

  if (loading) return (
    <div className="flex items-center justify-center min-h-[400px]">
      <motion.div
        className="flex items-center gap-3 text-accent"
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-6 border-2 border-accent border-t-transparent rounded-full animate-spin" />
        <span className="text-lg font-medium">Loading applications...</span>
      </motion.div>
    </div>
  )

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id)
  }

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <motion.div
      className="p-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <motion.div
        className="mb-8"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-accent to-accent/80 bg-clip-text text-transparent">
          Join Applications
        </h1>
        <p className="text-snow/70">Manage and review membership applications</p>
      </motion.div>

      <div className="grid gap-6">
        {applications.map((app, index) => (
          <motion.div
            key={app.id}
            className="bg-white/[0.02] border border-white/10 rounded-2xl p-6 backdrop-blur-sm
                     hover:bg-white/[0.04] hover:border-accent/20 transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            whileHover={{ y: -2, scale: 1.01 }}
          >
            <motion.div
              className="flex justify-between items-start mb-4 cursor-pointer group"
              onClick={() => toggleExpand(app.id)}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
            >
              <div className="flex items-start gap-4">
                <motion.div
                  className="p-3 bg-accent/10 rounded-xl border border-accent/20"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <User className="w-5 h-5 text-accent" />
                </motion.div>
                <div>
                  <h2 className="text-xl font-semibold text-snow group-hover:text-accent transition-colors">
                    {app.full_name}
                  </h2>
                  <div className="flex items-center gap-2 text-sm text-snow/60 mt-1">
                    <Clock className="w-4 h-4" />
                    {formatDate(app.created_at)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <motion.span
                  className={`px-4 py-2 rounded-xl text-sm font-medium border ${
                    app.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
                    app.status === 'approved' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                    'bg-red-500/20 text-red-400 border-red-500/30'
                  }`}
                  whileHover={{ scale: 1.05 }}
                >
                  {app.status}
                </motion.span>
                <motion.div
                  animate={{ rotate: expandedId === app.id ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-snow/60 group-hover:text-accent transition-colors"
                >
                  <ChevronDown className="w-5 h-5" />
                </motion.div>
              </div>
            </motion.div>

            <AnimatePresence>
              {expandedId === app.id && (
                <motion.div
                  className="grid gap-6 pt-6 border-t border-white/10 mt-4"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Mail className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Email</label>
                      </div>
                      <p className="text-snow/90">{app.email}</p>
                    </motion.div>

                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Phone className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Phone</label>
                      </div>
                      <p className="text-snow/90">{app.phone || 'N/A'}</p>
                    </motion.div>

                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Globe className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Website</label>
                      </div>
                      <p className="text-snow/90">{app.website ? (
                        <a href={app.website} target="_blank" rel="noopener noreferrer"
                           className="text-accent hover:text-accent/80 hover:underline transition-colors">
                          {app.website}
                        </a>
                      ) : 'N/A'}</p>
                    </motion.div>

                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Experience</label>
                      </div>
                      <p className="text-snow/90">{app.experience || 'N/A'}</p>
                    </motion.div>

                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Briefcase className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Position</label>
                      </div>
                      <p className="text-snow/90">{app.job_title} at {app.company}</p>
                    </motion.div>

                    <motion.div
                      className="bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <User className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Engagement Type</label>
                      </div>
                      <p className="text-snow/90">{app.engagement_type}</p>
                    </motion.div>
                  </div>

                  <motion.div
                    className="md:col-span-2 bg-white/[0.02] rounded-xl p-4 border border-white/10"
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="w-4 h-4 text-accent" />
                      <label className="text-sm text-accent font-medium">Service Interest</label>
                    </div>
                    <p className="text-snow/90">{app.service_interest}</p>
                  </motion.div>

                  <motion.div
                    className="md:col-span-2 bg-white/[0.02] rounded-xl p-4 border border-white/10"
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <User className="w-4 h-4 text-accent" />
                      <label className="text-sm text-accent font-medium">Motivation</label>
                    </div>
                    <p className="whitespace-pre-wrap text-snow/90 leading-relaxed">{app.motivation || 'N/A'}</p>
                  </motion.div>

                  <motion.div
                    className="md:col-span-2 bg-white/[0.02] rounded-xl p-4 border border-white/10"
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="w-4 h-4 text-accent" />
                      <label className="text-sm text-accent font-medium">AI/LLM Experience</label>
                    </div>
                    <p className="whitespace-pre-wrap text-snow/90 leading-relaxed">{app.ai_experience || 'N/A'}</p>
                  </motion.div>

                  {app.projects && (
                    <motion.div
                      className="md:col-span-2 bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Briefcase className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Projects</label>
                      </div>
                      <p className="whitespace-pre-wrap text-snow/90 leading-relaxed">{app.projects}</p>
                    </motion.div>
                  )}

                  {app.goals && (
                    <motion.div
                      className="md:col-span-2 bg-white/[0.02] rounded-xl p-4 border border-white/10"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="w-4 h-4 text-accent" />
                        <label className="text-sm text-accent font-medium">Goals</label>
                      </div>
                      <p className="whitespace-pre-wrap text-snow/90 leading-relaxed">{app.goals}</p>
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}
