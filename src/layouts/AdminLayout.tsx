import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { ArrowLef<PERSON>, Settings, Users } from 'lucide-react'

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gradient-to-b from-midnight to-midnight/95 text-snow">
      {/* Modern Navigation */}
      <motion.nav
        className="border-b border-white/10 backdrop-blur-xl bg-midnight/80 px-6 py-4"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <motion.div
              className="flex items-center gap-4"
              whileHover={{ scale: 1.02 }}
            >
              <Link
                to="/"
                className="flex items-center gap-2 text-xl font-bold text-snow hover:text-accent
                         transition-colors duration-300"
              >
                <img src="/latest_logo2_black_NObg.png" alt="Sainpse Logo" className="w-6 h-6" />
                Sainpse
              </Link>
              <div className="h-6 w-px bg-white/20" />
              <div className="flex items-center gap-2 text-accent/80">
                <Settings className="w-4 h-4" />
                <span className="text-sm font-medium">Admin Panel</span>
              </div>
            </motion.div>

            <div className="flex items-center gap-3">
              <motion.div
                whileHover={{ scale: 1.02, x: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link
                  to="/"
                  className="inline-flex items-center px-3 py-2 rounded-xl bg-white/[0.03] hover:bg-white/[0.06]
                    border border-white/10 hover:border-accent/20 transition-all duration-300 text-snow/70
                    hover:text-accent group backdrop-blur-sm text-sm"
                >
                  <motion.div
                    className="mr-2"
                    whileHover={{ x: -2 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ArrowLeft className="w-4 h-4" />
                  </motion.div>
                  <span className="font-medium">Back to Site</span>
                </Link>
              </motion.div>

              <motion.div
                className="flex items-center gap-2 px-3 py-2 rounded-xl bg-accent/10 border border-accent/20"
                whileHover={{ scale: 1.02 }}
              >
                <Users className="w-4 h-4 text-accent" />
                <span className="text-sm text-accent font-medium">Admin</span>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Main Content */}
      <main className="container mx-auto relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {children}
        </motion.div>
      </main>
    </div>
  )
}
